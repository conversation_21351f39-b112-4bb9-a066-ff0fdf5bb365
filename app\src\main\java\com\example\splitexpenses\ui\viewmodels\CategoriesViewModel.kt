package com.example.splitexpenses.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.splitexpenses.data.Category
import com.example.splitexpenses.data.repositories.GroupRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the categories management screen
 */
@HiltViewModel
class CategoriesViewModel @Inject constructor(
    private val groupRepository: GroupRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CategoriesUiState())
    val uiState: StateFlow<CategoriesUiState> = _uiState.asStateFlow()

    init {
        // Load categories from the current group
        viewModelScope.launch {
            groupRepository.currentGroup.collectLatest { group ->
                if (group != null) {
                    _uiState.update {
                        it.copy(categories = group.categories)
                    }
                }
            }
        }
    }

    /**
     * Update the new category name
     */
    fun updateNewCategoryName(name: String) {
        _uiState.update { it.copy(newCategoryName = name) }
    }

    /**
     * Update the new category emoji
     */
    fun updateNewCategoryEmoji(emoji: String) {
        if (emoji.length <= 2) {
            _uiState.update { it.copy(newCategoryEmoji = emoji) }
        }
    }

    /**
     * Update the new keyword input field
     */
    fun updateNewKeywordInput(input: String) {
        _uiState.update { it.copy(newKeywordInput = input) }
    }

    /**
     * Add a keyword to the new category
     */
    fun addNewKeyword() {
        val currentState = uiState.value
        val keyword = currentState.newKeywordInput.trim()

        if (keyword.isNotEmpty() && keyword !in currentState.newCategoryKeywords) {
            _uiState.update {
                it.copy(
                    newCategoryKeywords = it.newCategoryKeywords + keyword,
                    newKeywordInput = ""
                )
            }
        }
    }

    /**
     * Remove a keyword from the new category
     */
    fun removeNewKeyword(keyword: String) {
        _uiState.update {
            it.copy(newCategoryKeywords = it.newCategoryKeywords - keyword)
        }
    }

    /**
     * Add a new category
     */
    fun addCategory() {
        val currentState = uiState.value

        if (currentState.newCategoryName.isBlank() ||
            currentState.newCategoryEmoji.isBlank()) {
            _uiState.update { it.copy(showError = true) }
            return
        }

        // Automatically include the category name as a keyword (lowercase)
        val categoryNameKeyword = currentState.newCategoryName.lowercase().trim()
        val allKeywords = (currentState.newCategoryKeywords + categoryNameKeyword).distinct()

        val newCategory = Category(
            name = currentState.newCategoryName,
            emoji = currentState.newCategoryEmoji,
            keywords = allKeywords
        )

        val updatedCategories = currentState.categories + newCategory

        _uiState.update {
            it.copy(
                categories = updatedCategories,
                newCategoryName = "",
                newCategoryEmoji = "",
                newCategoryKeywords = emptyList(),
                newKeywordInput = "",
                showError = false
            )
        }
    }

    /**
     * Delete a category (prevents deletion of the default "None" category)
     */
    fun deleteCategory(category: Category) {
        // Prevent deletion of the default "None" category
        if (category.name == "None") {
            return
        }

        val currentState = uiState.value
        val updatedCategories = currentState.categories.filter { it != category }

        _uiState.update { it.copy(categories = updatedCategories) }
    }

    /**
     * Start editing a category
     */
    fun startEditingCategory(category: Category) {
        _uiState.update {
            it.copy(
                editingCategory = category,
                editName = category.name,
                editEmoji = category.emoji,
                editKeywords = category.keywords,
                editKeywordInput = ""
            )
        }
    }

    /**
     * Update the edit name
     */
    fun updateEditName(name: String) {
        _uiState.update { it.copy(editName = name) }
    }

    /**
     * Update the edit emoji
     */
    fun updateEditEmoji(emoji: String) {
        if (emoji.length <= 2) {
            _uiState.update { it.copy(editEmoji = emoji) }
        }
    }

    /**
     * Update the edit keyword input field
     */
    fun updateEditKeywordInput(input: String) {
        _uiState.update { it.copy(editKeywordInput = input) }
    }

    /**
     * Add a keyword to the editing category
     */
    fun addEditKeyword() {
        val currentState = uiState.value
        val keyword = currentState.editKeywordInput.trim()

        if (keyword.isNotEmpty() && keyword !in currentState.editKeywords) {
            _uiState.update {
                it.copy(
                    editKeywords = it.editKeywords + keyword,
                    editKeywordInput = ""
                )
            }
        }
    }

    /**
     * Remove a keyword from the editing category
     */
    fun removeEditKeyword(keyword: String) {
        _uiState.update {
            it.copy(editKeywords = it.editKeywords - keyword)
        }
    }

    /**
     * Save the edited category
     */
    fun saveEditedCategory() {
        val currentState = uiState.value
        val editingCategory = currentState.editingCategory ?: return

        // Automatically include the category name as a keyword (lowercase)
        val categoryNameKeyword = currentState.editName.lowercase().trim()
        val allKeywords = (currentState.editKeywords + categoryNameKeyword).distinct()

        val updatedCategory = Category(
            name = currentState.editName,
            emoji = currentState.editEmoji,
            keywords = allKeywords
        )

        val updatedCategories = currentState.categories.map {
            if (it == editingCategory) updatedCategory else it
        }

        _uiState.update {
            it.copy(
                categories = updatedCategories,
                editingCategory = null
            )
        }
    }

    /**
     * Cancel editing a category
     */
    fun cancelEditingCategory() {
        _uiState.update { it.copy(editingCategory = null) }
    }

    /**
     * Save all categories to the current group
     */
    fun saveCategories() {
        viewModelScope.launch {
            try {
                val currentGroup = groupRepository.currentGroup.value ?: return@launch

                // Update the group with the new categories
                groupRepository.updateGroupCategories(currentGroup.id, uiState.value.categories)
            } catch (e: Exception) {
                println("Error saving categories: ${e.message}")
                _uiState.update { it.copy(error = "Failed to save categories: ${e.message}") }
            }
        }
    }

    /**
     * Clear the error state
     */
    fun clearError() {
        _uiState.update { it.copy(showError = false) }
    }
}
