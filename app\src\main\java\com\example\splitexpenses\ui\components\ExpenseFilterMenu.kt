package com.example.splitexpenses.ui.components

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.SizeTransform
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.animation.with
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DateRangePicker
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RangeSlider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.rememberDateRangePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.splitexpenses.data.Category
import com.example.splitexpenses.ui.viewmodels.ExpenseFilterState
import com.example.splitexpenses.ui.viewmodels.PeriodType
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class,
    ExperimentalAnimationApi::class
)
@Composable
fun ExpenseFilterMenu(
    isVisible: Boolean,
    filterState: ExpenseFilterState,
    availableCategories: List<Category>,
    availableMembers: List<String>,
    allExpenses: List<com.example.splitexpenses.data.Expense>, // Add this parameter to calculate range
    onFilterStateChange: (ExpenseFilterState) -> Unit,
    onDismiss: () -> Unit
) {
    var showDatePicker by remember { mutableStateOf(false) }
    val dateFormat = remember { SimpleDateFormat("dd/MM/yy", Locale.getDefault()) }

    // State for swipe gesture
    var targetOffsetX by remember { mutableFloatStateOf(0f) }
    var isDragging by remember { mutableStateOf(false) }

    // State for month/year navigation animation direction
    var animationDirection by remember { mutableStateOf(0) } // -1 = backward, 1 = forward, 0 = none

    // Animated offset for smooth transitions
    val animatedOffsetX by animateFloatAsState(
        targetValue = targetOffsetX,
        animationSpec = if (isDragging) {
            // No animation while dragging for immediate response
            tween(0)
        } else {
            // Smooth animation when snapping back
            tween(200, easing = FastOutSlowInEasing)
        },
        label = "swipeOffset"
    )

    // Reset offset when menu becomes visible
    if (isVisible && targetOffsetX != 0f) {
        targetOffsetX = 0f
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = slideInHorizontally(
            initialOffsetX = { fullWidth -> fullWidth },
            animationSpec = tween(300, easing = FastOutSlowInEasing)
        ),
        exit = slideOutHorizontally(
            targetOffsetX = { fullWidth -> fullWidth },
            animationSpec = tween(300, easing = FastOutSlowInEasing)
        )
    ) {
        // Semi-transparent background with click-to-dismiss
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.5f))
                .clickable(
                    interactionSource = remember { androidx.compose.foundation.interaction.MutableInteractionSource() },
                    indication = null
                ) {
                    onDismiss()
                }
        ) {
            // Filter menu panel with swipe gesture
            Surface(
                modifier = Modifier
                    .fillMaxHeight()
                    .width(350.dp)
                    .align(Alignment.CenterEnd)
                    .offset(x = animatedOffsetX.dp)
                    .pointerInput(Unit) {
                        detectDragGestures(
                            onDragStart = {
                                isDragging = true
                            },
                            onDragEnd = {
                                isDragging = false
                                // If swiped more than 100dp to the right, dismiss
                                if (targetOffsetX > 100f) {
                                    onDismiss()
                                } else {
                                    // Snap back to original position
                                    targetOffsetX = 0f
                                }
                            }
                        ) { _, dragAmount ->
                            // Only allow rightward swipes (positive X)
                            val newOffset = targetOffsetX + dragAmount.x
                            if (newOffset >= 0) {
                                targetOffsetX = newOffset
                            }
                        }
                    }
                    .clickable(
                        interactionSource = remember { androidx.compose.foundation.interaction.MutableInteractionSource() },
                        indication = null
                    ) {
                        // Prevent click-through to background
                    },
                color = MaterialTheme.colorScheme.surface,
                shadowElevation = 8.dp
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                        .verticalScroll(rememberScrollState())
                        .animateContentSize()
                ) {
                    // Header
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Filter Expenses",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold
                        )
                        IconButton(onClick = onDismiss) {
                            Icon(Icons.Default.Clear, contentDescription = "Close")
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Search Section
                    Text(
                        text = "Search",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = filterState.searchText,
                        colors = TextFieldDefaults.colors(
                            unfocusedContainerColor = Color.Transparent,
                            focusedContainerColor = Color.Transparent,
                            unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                        ),
                        onValueChange = { newText ->
                            onFilterStateChange(filterState.copy(searchText = newText))
                        },
                        label = { Text("Search description") },
                        leadingIcon = {
                            Icon(Icons.Default.Search, contentDescription = "Search")
                        },
                        trailingIcon = if (filterState.searchText.isNotBlank()) {
                            {
                                IconButton(
                                    onClick = {
                                        onFilterStateChange(filterState.copy(searchText = ""))
                                    }
                                ) {
                                    Icon(Icons.Default.Clear, contentDescription = "Clear search")
                                }
                            }
                        } else null,
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Amount Range Filter Section
                    Text(
                        text = "Amount Range",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    // Calculate min and max amounts from all expenses
                    val expenseAmounts = allExpenses.map { it.amount.toFloat() }
                    val globalMinAmount = expenseAmounts.minOrNull() ?: 0f
                    val globalMaxAmount = expenseAmounts.maxOrNull() ?: 100f

                    // Ensure we have a reasonable range
                    val minRange = if (globalMinAmount == globalMaxAmount) 0f else globalMinAmount
                    val maxRange = if (globalMinAmount == globalMaxAmount) globalMinAmount + 100f else globalMaxAmount

                    // Current slider values
                    val currentMinAmount = filterState.minAmount ?: minRange
                    val currentMaxAmount = filterState.maxAmount ?: maxRange

                    // Amount range display
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "${String.format("%.0f", currentMinAmount)}€",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "${String.format("%.0f", currentMaxAmount)}€",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    // Range slider
                    if (maxRange > minRange) {
                        RangeSlider(
                            value = currentMinAmount..currentMaxAmount,
                            onValueChange = { range ->
                                onFilterStateChange(
                                    filterState.copy(
                                        minAmount = if (range.start == minRange) null else range.start,
                                        maxAmount = if (range.endInclusive == maxRange) null else range.endInclusive
                                    )
                                )
                            },
                            valueRange = minRange..maxRange,
                            modifier = Modifier.fillMaxWidth(),
                            colors = SliderDefaults.colors(
                                thumbColor = MaterialTheme.colorScheme.primary,
                                activeTrackColor = MaterialTheme.colorScheme.primary,
                                inactiveTrackColor = MaterialTheme.colorScheme.surfaceVariant
                            )
                        )
                    } else {
                        // Show message when no expenses or all same amount
                        Text(
                            text = if (allExpenses.isEmpty()) "No expenses to filter" else "All expenses have the same amount",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // Date Filter Section (matching Statistics screen)
                    Text(
                        text = "Period",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    // Period filter buttons (matching Statistics screen layout)
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // All Time button
                        Button(
                            onClick = {
                                onFilterStateChange(
                                    filterState.copy(periodType = PeriodType.ALL_TIME)
                                )
                            },
                            modifier = Modifier
                                .height(44.dp)
                                .weight(1f),
                            shape = MaterialTheme.shapes.extraLarge,
                            colors = androidx.compose.material3.ButtonDefaults.buttonColors(
                                containerColor = if (filterState.periodType == PeriodType.ALL_TIME)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.surfaceVariant,
                                contentColor = if (filterState.periodType == PeriodType.ALL_TIME)
                                    MaterialTheme.colorScheme.onPrimary
                                else
                                    MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        ) {
                            Text("All", style = MaterialTheme.typography.bodyLarge)
                        }

                        // Month button
                        Button(
                            onClick = {
                                val currentCalendar = Calendar.getInstance()
                                onFilterStateChange(
                                    filterState.copy(
                                        periodType = PeriodType.THIS_MONTH,
                                        selectedMonth = currentCalendar.get(Calendar.MONTH),
                                        selectedYear = currentCalendar.get(Calendar.YEAR)
                                    )
                                )
                            },
                            modifier = Modifier
                                .height(44.dp)
                                .weight(1f),
                            contentPadding = PaddingValues(0.dp),
                            shape = MaterialTheme.shapes.extraLarge,
                            colors = androidx.compose.material3.ButtonDefaults.buttonColors(
                                containerColor = if (filterState.periodType == PeriodType.THIS_MONTH)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.surfaceVariant,
                                contentColor = if (filterState.periodType == PeriodType.THIS_MONTH)
                                    MaterialTheme.colorScheme.onPrimary
                                else
                                    MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        ) {
                            Text("Month", style = MaterialTheme.typography.bodyLarge)//, overflow = TextOverflow.Ellipsis
                        }

                        // Year button
                        Button(
                            onClick = {
                                val currentCalendar = Calendar.getInstance()
                                onFilterStateChange(
                                    filterState.copy(
                                        periodType = PeriodType.THIS_YEAR,
                                        selectedYear = currentCalendar.get(Calendar.YEAR)
                                    )
                                )
                            },
                            modifier = Modifier
                                .height(44.dp)
                                .weight(1f),
                            shape = MaterialTheme.shapes.extraLarge,
                            colors = androidx.compose.material3.ButtonDefaults.buttonColors(
                                containerColor = if (filterState.periodType == PeriodType.THIS_YEAR)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.surfaceVariant,
                                contentColor = if (filterState.periodType == PeriodType.THIS_YEAR)
                                    MaterialTheme.colorScheme.onPrimary
                                else
                                    MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        ) {
                            Text("Year", style = MaterialTheme.typography.bodyLarge)
                        }

                        // Custom date range button
                        Surface(
                            modifier = Modifier
                                .width(44.dp)
                                .height(44.dp)
                                .clickable {
                                    onFilterStateChange(
                                        filterState.copy(periodType = PeriodType.CUSTOM)
                                    )
                                    showDatePicker = true
                                },
                            shape = MaterialTheme.shapes.extraLarge,
                            color = if (filterState.periodType == PeriodType.CUSTOM)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.surfaceVariant
                        ) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    Icons.Default.DateRange,
                                    contentDescription = "Custom Date Range",
                                    tint = if (filterState.periodType == PeriodType.CUSTOM)
                                        MaterialTheme.colorScheme.onPrimary
                                    else
                                        MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }

                    Column(modifier = Modifier.animateContentSize())
                    {
                        // Month/Year navigation for THIS_MONTH and THIS_YEAR with animations
                        if (filterState.periodType == PeriodType.THIS_MONTH || filterState.periodType == PeriodType.THIS_YEAR) {
                            Spacer(modifier = Modifier.height(8.dp))

                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(48.dp)
                                    .padding(horizontal = 8.dp),
                                horizontalArrangement = Arrangement.Center,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // Previous button with styling matching Statistics
                                Surface(
                                    modifier = Modifier.size(36.dp),
                                    shape = MaterialTheme.shapes.extraLarge,
                                    color = MaterialTheme.colorScheme.primaryContainer,
                                ) {
                                    IconButton(
                                        onClick = {
                                            animationDirection = -1 // Backward animation
                                            if (filterState.periodType == PeriodType.THIS_MONTH) {
                                                val newMonth = if (filterState.selectedMonth == 0) 11 else filterState.selectedMonth - 1
                                                val newYear = if (filterState.selectedMonth == 0) filterState.selectedYear - 1 else filterState.selectedYear
                                                onFilterStateChange(
                                                    filterState.copy(
                                                        selectedMonth = newMonth,
                                                        selectedYear = newYear
                                                    )
                                                )
                                            } else {
                                                onFilterStateChange(
                                                    filterState.copy(selectedYear = filterState.selectedYear - 1)
                                                )
                                            }
                                        },
                                        modifier = Modifier.fillMaxSize()
                                    ) {
                                        Icon(
                                            Icons.AutoMirrored.Filled.KeyboardArrowLeft,
                                            contentDescription = "Previous ${if (filterState.periodType == PeriodType.THIS_MONTH) "Month" else "Year"}",
                                            tint = MaterialTheme.colorScheme.primary
                                        )
                                    }
                                }

                                // Animated current period display
                                val currentText = when (filterState.periodType) {
                                    PeriodType.THIS_MONTH -> {
                                        val cal = Calendar.getInstance().apply {
                                            set(Calendar.YEAR, filterState.selectedYear)
                                            set(Calendar.MONTH, filterState.selectedMonth)
                                        }
                                        val monthName = cal.getDisplayName(Calendar.MONTH, Calendar.LONG, java.util.Locale.getDefault())
                                        "$monthName ${filterState.selectedYear}"
                                    }
                                    PeriodType.THIS_YEAR -> filterState.selectedYear.toString()
                                    else -> ""
                                }

                                Surface(
                                    modifier = Modifier
                                        .padding(horizontal = 4.dp)
                                        .width(140.dp)
                                        .height(36.dp),
                                    color = MaterialTheme.colorScheme.primaryContainer,
                                    shape = MaterialTheme.shapes.extraLarge
                                ) {
                                    Box(
                                        contentAlignment = Alignment.Center
                                    ) {
                                        // Animate text changes with explicit direction (matching Statistics)
                                        AnimatedContent(
                                            targetState = currentText,
                                            transitionSpec = {
                                                // Use explicit animation direction
                                                when (animationDirection) {
                                                    1 -> { // Forward
                                                        // Going forward (next month/year) - slide up
                                                        (slideInVertically { height -> height } + fadeIn())
                                                            .togetherWith(slideOutVertically { height -> -height } + fadeOut())
                                                    }
                                                    -1 -> { // Backward
                                                        // Going backward (previous month/year) - slide down
                                                        (slideInVertically { height -> -height } + fadeIn())
                                                            .togetherWith(slideOutVertically { height -> height } + fadeOut())
                                                    }
                                                    else -> {
                                                        fadeIn().togetherWith(fadeOut())
                                                    }
                                                }.using(
                                                    SizeTransform(clip = false)
                                                )
                                            },
                                            label = "periodTextAnimation"
                                        ) { text ->
                                            Box(
                                                modifier = Modifier.fillMaxWidth(),
                                                contentAlignment = Alignment.Center
                                            ) {
                                                Text(
                                                    text = text,
                                                    style = MaterialTheme.typography.titleMedium,
                                                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                                                    textAlign = TextAlign.Center
                                                )
                                            }
                                        }
                                    }
                                }

                                // Next button with styling matching Statistics
                                Surface(
                                    modifier = Modifier.size(36.dp),
                                    shape = MaterialTheme.shapes.extraLarge,
                                    color = MaterialTheme.colorScheme.primaryContainer,
                                ) {
                                    IconButton(
                                        onClick = {
                                            animationDirection = 1 // Forward animation
                                            if (filterState.periodType == PeriodType.THIS_MONTH) {
                                                val newMonth = if (filterState.selectedMonth == 11) 0 else filterState.selectedMonth + 1
                                                val newYear = if (filterState.selectedMonth == 11) filterState.selectedYear + 1 else filterState.selectedYear
                                                onFilterStateChange(
                                                    filterState.copy(
                                                        selectedMonth = newMonth,
                                                        selectedYear = newYear
                                                    )
                                                )
                                            } else {
                                                onFilterStateChange(
                                                    filterState.copy(selectedYear = filterState.selectedYear + 1)
                                                )
                                            }
                                        },
                                        modifier = Modifier.fillMaxSize()
                                    ) {
                                        Icon(
                                            Icons.AutoMirrored.Filled.KeyboardArrowRight,
                                            contentDescription = "Next ${if (filterState.periodType == PeriodType.THIS_MONTH) "Month" else "Year"}",
                                            tint = MaterialTheme.colorScheme.primary
                                        )
                                    }
                                }
                            }
                        }
                    }

                    // Show selected date range for custom period
                    Column (modifier = Modifier.animateContentSize())
                    {
                        if (filterState.periodType == PeriodType.CUSTOM &&
                            filterState.startDate != null && filterState.endDate != null
                        ) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "${dateFormat.format(Date(filterState.startDate))} - ${
                                    dateFormat.format(
                                        Date(filterState.endDate)
                                    )
                                }",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.fillMaxWidth(),
                                textAlign = androidx.compose.ui.text.style.TextAlign.Center
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(14.dp))

                    // Category Filter Section
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(32.dp), // Fixed height to prevent layout shifts
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Categories",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )

                        // Clear categories button with animation - using Box to maintain space
                        Box(
                            modifier = Modifier.height(32.dp),
                            contentAlignment = Alignment.CenterEnd
                        ) {
                            androidx.compose.animation.AnimatedVisibility(
                                visible = filterState.selectedCategories.isNotEmpty(),
                                enter = slideInHorizontally(initialOffsetX = { fullWidth -> fullWidth }) + fadeIn(),
                                exit = slideOutHorizontally(targetOffsetX = { fullWidth -> fullWidth }) + fadeOut()
                            ) {
                                TextButton(
                                    onClick = { onFilterStateChange(filterState.copy(selectedCategories = emptySet())) },
                                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                                ) {
                                    Text(
                                        text = "Clear",
                                        style = MaterialTheme.typography.labelMedium,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }
                    }
                    Spacer(modifier = Modifier.height(8.dp))

                    // Animated category chips container
                    AnimatedVisibility(
                        visible = availableCategories.isNotEmpty(),
                        enter = fadeIn(animationSpec = tween(300)) + expandVertically(
                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                        ),
                        exit = fadeOut(animationSpec = tween(200)) + shrinkVertically(
                            animationSpec = tween(200, easing = FastOutSlowInEasing)
                        )
                    ) {
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            contentPadding = PaddingValues(vertical = 4.dp),
                            modifier = Modifier.animateContentSize(
                                animationSpec = tween(300, easing = FastOutSlowInEasing)
                            )
                        ) {
                            items(availableCategories, key = { it.name }) { category ->
                                val isSelected = category.name in filterState.selectedCategories

                                // Animate chip selection with scale and color
                                val scale by animateFloatAsState(
                                    targetValue = if (isSelected) 1.05f else 1f,
                                    animationSpec = tween(200, easing = FastOutSlowInEasing),
                                    label = "chipScale"
                                )

                                FilterChip(
                                    selected = isSelected,
                                    onClick = {
                                        val newCategories = if (isSelected) {
                                            filterState.selectedCategories - category.name
                                        } else {
                                            filterState.selectedCategories + category.name
                                        }
                                        onFilterStateChange(filterState.copy(selectedCategories = newCategories))
                                    },
                                    label = {
                                        Text(
                                            "${category.emoji} ${category.name}",
                                            modifier = Modifier.animateContentSize(
                                                animationSpec = tween(
                                                    200,
                                                    easing = FastOutSlowInEasing
                                                )
                                            )
                                        )
                                    },
                                    border = FilterChipDefaults.filterChipBorder(
                                        selected = isSelected,
                                        enabled = true,
                                        borderColor = MaterialTheme.colorScheme.secondaryContainer
                                    ),
                                    colors = FilterChipDefaults.filterChipColors(
                                        selectedContainerColor = MaterialTheme.colorScheme.secondaryContainer
                                    ),
                                    modifier = Modifier
                                        .animateItemPlacement(
                                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                                        )
                                        .graphicsLayer {
                                            scaleX = scale
                                            scaleY = scale
                                        }
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(14.dp))

                    // Member Filter Section
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(32.dp), // Fixed height to prevent layout shifts
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Members",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )

                        // Clear members button with animation - using Box to maintain space
                        Box(
                            modifier = Modifier.height(32.dp),
                            contentAlignment = Alignment.CenterEnd
                        ) {
                            androidx.compose.animation.AnimatedVisibility(
                                visible = filterState.selectedMembers.isNotEmpty(),
                                enter = slideInHorizontally(initialOffsetX = { fullWidth -> fullWidth }) + fadeIn(),
                                exit = slideOutHorizontally(targetOffsetX = { fullWidth -> fullWidth }) + fadeOut()
                            ) {
                                TextButton(
                                    onClick = {
                                        onFilterStateChange(filterState.copy(selectedMembers = emptySet()))
                                    },
                                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                                ) {
                                    Text(
                                        text = "Clear",
                                        style = MaterialTheme.typography.labelMedium,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }
                    }
                    Spacer(modifier = Modifier.height(8.dp))

                    // Animated member chips container
                    AnimatedVisibility(
                        visible = availableMembers.isNotEmpty(),
                        enter = fadeIn(animationSpec = tween(300)) + expandVertically(
                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                        ),
                        exit = fadeOut(animationSpec = tween(200)) + shrinkVertically(
                            animationSpec = tween(200, easing = FastOutSlowInEasing)
                        )
                    ) {
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            contentPadding = PaddingValues(vertical = 4.dp),
                            modifier = Modifier.animateContentSize(
                                animationSpec = tween(300, easing = FastOutSlowInEasing)
                            )
                        ) {
                            items(availableMembers, key = { it }) { member ->
                                val isSelected = member in filterState.selectedMembers

                                // Animate chip selection with scale and color
                                val scale by animateFloatAsState(
                                    targetValue = if (isSelected) 1.05f else 1f,
                                    animationSpec = tween(200, easing = FastOutSlowInEasing),
                                    label = "memberChipScale"
                                )

                                FilterChip(
                                    selected = isSelected,
                                    onClick = {
                                        val newMembers = if (isSelected) {
                                            filterState.selectedMembers - member
                                        } else {
                                            filterState.selectedMembers + member
                                        }
                                        onFilterStateChange(filterState.copy(selectedMembers = newMembers))
                                    },
                                    label = {
                                        Text(
                                            member,
                                            modifier = Modifier.animateContentSize(
                                                animationSpec = tween(
                                                    200,
                                                    easing = FastOutSlowInEasing
                                                )
                                            )
                                        )
                                    },
                                    border = FilterChipDefaults.filterChipBorder(
                                        selected = isSelected,
                                        enabled = true,
                                        borderColor = MaterialTheme.colorScheme.secondaryContainer
                                    ),
                                    colors = FilterChipDefaults.filterChipColors(
                                        selectedContainerColor = MaterialTheme.colorScheme.secondaryContainer
                                    ),
                                    modifier = Modifier
                                        .animateItemPlacement(
                                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                                        )
                                        .graphicsLayer {
                                            scaleX = scale
                                            scaleY = scale
                                        }
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(32.dp))

                    // Clear all filters button with animation
                    AnimatedVisibility(
                        visible = filterState.hasActiveFilters(),
                        enter = fadeIn(animationSpec = tween(300)) + expandVertically(
                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                        ),
                        exit = fadeOut(animationSpec = tween(200)) + shrinkVertically(
                            animationSpec = tween(200, easing = FastOutSlowInEasing)
                        )
                    ) {
                        Button(
                            onClick = {
                                onFilterStateChange(ExpenseFilterState())
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .animateContentSize(
                                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                                )
                        ) {
                            Text("Clear All Filters")
                        }
                    }
                }
            }
        }
    }

    // Date picker dialog
    if (showDatePicker) {
        val dateRangePickerState = rememberDateRangePickerState(
            initialSelectedStartDateMillis = filterState.startDate,
            initialSelectedEndDateMillis = filterState.endDate
        )
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                TextButton(
                    onClick = {
                        onFilterStateChange(
                            filterState.copy(
                                startDate = dateRangePickerState.selectedStartDateMillis,
                                endDate = dateRangePickerState.selectedEndDateMillis
                            )
                        )
                        showDatePicker = false
                    }
                ) {
                    Text("OK")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDatePicker = false }) {
                    Text("Cancel")
                }
            }
        ) {
            DateRangePicker(
                state = dateRangePickerState,
                title = {
                    Text(
                        modifier = Modifier.padding(16.dp),
                        text = "Select date range"
                    )
                },
                showModeToggle = false,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(500.dp)
                    .padding(2.dp)
            )
        }
    }
}
