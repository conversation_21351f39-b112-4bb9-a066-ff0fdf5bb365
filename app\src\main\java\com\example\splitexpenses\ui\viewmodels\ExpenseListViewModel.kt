package com.example.splitexpenses.ui.viewmodels

import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.data.repositories.ExpenseRepository
import com.example.splitexpenses.data.repositories.GroupRepository
import com.example.splitexpenses.data.connectivity.NetworkConnectivityManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.OutputStream
import java.util.Calendar
import javax.inject.Inject

/**
 * UI state for the expense list screen
 */
data class ExpenseListUiState(
    val selectedExpenses: Set<String> = emptySet(),
    val isMultiSelectMode: Boolean = false,
    val filterState: ExpenseFilterState = ExpenseFilterState(),
    override val isLoading: Boolean = false,
    override val error: String? = null
) : UiState

/**
 * ViewModel for the expense list screen
 */
@HiltViewModel
class ExpenseListViewModel @Inject constructor(
    private val expenseRepository: ExpenseRepository,
    val groupRepository: GroupRepository,  // Changed from private to public
    connectivityManager: NetworkConnectivityManager
) : OfflineAwareViewModel<ExpenseListUiState>(connectivityManager) {

    // Expose the current group as a StateFlow
    val currentGroup: StateFlow<GroupData?> = groupRepository.currentGroup

    override fun createInitialState(): ExpenseListUiState {
        return ExpenseListUiState()
    }

    /**
     * Add a new expense
     * @param amount The amount of the expense
     * @param description The description of the expense
     * @param paidBy The name of the person who paid
     * @param splitBetween The names of the people to split the expense between
     * @param category The category of the expense
     * @param date The date of the expense
     * @param isCategoryLocked Whether the category is locked and should not be auto-detected
     */
    fun addExpense(
        amount: Double,
        description: String,
        paidBy: String,
        splitBetween: List<String>,
        category: String,
        date: Long,
        isCategoryLocked: Boolean = false
    ) {
        // Allow creating new expenses even when offline
        executeCreateOperation(
            operation = {
                expenseRepository.addExpense(
                    amount = amount,
                    description = description,
                    paidBy = paidBy,
                    splitBetween = splitBetween,
                    category = category,
                    date = date,
                    isCategoryLocked = isCategoryLocked
                )
            }
        )
    }

    /**
     * Update an existing expense
     * @param expenseId The ID of the expense to update
     * @param amount The new amount
     * @param description The new description
     * @param paidBy The new payer
     * @param splitBetween The new split
     * @param category The new category
     * @param date The new date
     * @param isCategoryLocked Whether the category is locked and should not be auto-detected
     */
    fun updateExpense(
        expenseId: String,
        amount: Double,
        description: String,
        paidBy: String,
        splitBetween: List<String>,
        category: String,
        date: Long,
        isCategoryLocked: Boolean = false
    ) {
        // Prevent editing existing expenses when offline
        executeEditOperationIfConnected(
            operation = {
                expenseRepository.updateExpense(
                    expenseId = expenseId,
                    amount = amount,
                    description = description,
                    paidBy = paidBy,
                    splitBetween = splitBetween,
                    category = category,
                    date = date,
                    isCategoryLocked = isCategoryLocked
                )
            }
        )
    }

    /**
     * Delete an expense
     * @param expenseId The ID of the expense to delete
     * @return True if the expense was successfully deleted, false otherwise
     */
    suspend fun deleteExpense(expenseId: String): Boolean {
        return try {
            println("ExpenseListViewModel: Deleting expense with ID: $expenseId")
            if (expenseId.isEmpty()) {
                println("Cannot delete expense: Empty expense ID")
                return false
            }

            // Check if the expense exists in the current group
            val group = currentGroup.value
            if (group == null) {
                println("Cannot delete expense: No current group")
                return false
            }

            val expenseExists = group.expenses.any { it.id == expenseId }
            if (!expenseExists) {
                println("Cannot delete expense: Expense not found with ID $expenseId")
                return false
            }

            // Delete the expense
            expenseRepository.deleteExpense(expenseId)
            println("Expense deleted successfully: $expenseId")
            true
        } catch (e: Exception) {
            println("Error deleting expense: ${e.message}")
            e.printStackTrace()
            false
        }
    }

    /**
     * Delete multiple expenses
     * @param expenseIds The IDs of the expenses to delete
     */
    fun deleteExpenses(expenseIds: Set<String>) {
        // Prevent deleting existing expenses when offline
        executeEditOperationIfConnected(
            operation = {
                expenseRepository.deleteExpenses(expenseIds)

                // Update UI state
                updateState { state ->
                    state.copy(
                        selectedExpenses = emptySet(),
                        isMultiSelectMode = false
                    )
                }
            }
        )
    }

    /**
     * Toggle multi-select mode
     * @param enabled Whether multi-select mode should be enabled
     */
    fun setMultiSelectMode(enabled: Boolean) {
        updateState { state ->
            state.copy(
                isMultiSelectMode = enabled,
                selectedExpenses = if (!enabled) emptySet() else state.selectedExpenses
            )
        }
    }

    /**
     * Toggle selection of an expense
     * @param expenseId The ID of the expense to toggle
     * @param selected Whether the expense should be selected
     */
    fun toggleExpenseSelection(expenseId: String, selected: Boolean) {
        updateState { state ->
            val newSelectedExpenses = state.selectedExpenses.toMutableSet()

            if (selected) {
                newSelectedExpenses.add(expenseId)
            } else {
                newSelectedExpenses.remove(expenseId)
            }

            // Automatically disable multi-select mode when no items are selected
            val shouldDisableMultiSelect = newSelectedExpenses.isEmpty() && state.isMultiSelectMode

            state.copy(
                selectedExpenses = newSelectedExpenses,
                isMultiSelectMode = if (shouldDisableMultiSelect) false else state.isMultiSelectMode
            )
        }
    }

    /**
     * Export the current group to CSV
     * @param outputStream The output stream to write to
     * @return True if export was successful, false otherwise
     */
    fun exportToCsv(outputStream: OutputStream): Boolean {
        return groupRepository.exportGroupToCsv(outputStream)
    }

    /**
     * Delete the current group
     * @return True if the group was successfully deleted, false otherwise
     */
    suspend fun deleteCurrentGroup(): Boolean {
        try {
            val groupId = currentGroup.value?.id ?: return false
            println("ExpenseListViewModel: Deleting group with ID: $groupId")

            // Check if the current user is the creator
            if (!isCurrentUserGroupCreator()) {
                println("ExpenseListViewModel: Current user is not the creator of the group. Only the creator can delete the group.")
                return false
            }

            // Stop listening for updates to this group before deleting
            groupRepository.stopListeningForCurrentGroup()

            // Delete the group
            try {
                groupRepository.deleteGroup(groupId)
                // If we get here, the deletion was successful
            } catch (e: Exception) {
                println("ExpenseListViewModel: Failed to delete group: ${e.message}")
                return false
            }

            // Clear the current group selection
            // This will trigger UI updates through the StateFlow
            println("ExpenseListViewModel: Group deleted successfully, clearing current group")

            return true
        } catch (e: Exception) {
            println("ExpenseListViewModel: Error deleting group: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * Update the members of the current group
     * @param members The new list of members
     * @return True if the members were successfully updated, false otherwise
     */
    suspend fun updateGroupMembers(members: List<String>): Boolean {
        return try {
            println("ExpenseListViewModel: Updating members for current group")
            val currentGroupId = currentGroup.value?.id
            if (currentGroupId == null) {
                println("Cannot update members: No current group")
                return false
            }

            if (members.isEmpty()) {
                println("Cannot update members: Empty members list")
                return false
            }

            // Make sure the current user is still in the list
            val currentUserName = groupRepository.getSavedUserForGroup(currentGroupId)
            if (currentUserName != null && !members.contains(currentUserName)) {
                println("Cannot update members: Current user must remain in the group")
                return false
            }

            // Update the members
            try {
                groupRepository.updateGroupMembers(currentGroupId, members)
                println("Members updated successfully for group: $currentGroupId")
                return true
            } catch (e: Exception) {
                println("Failed to update members for group: $currentGroupId - ${e.message}")
                return false
            }
        } catch (e: Exception) {
            println("Error updating members: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * Remove a member from the group and kick them out (remove from allowedUsers)
     * This should only be called by the group creator
     * @param memberName The name of the member to remove
     * @return True if successful, false otherwise
     */
    suspend fun removeMemberAndKick(memberName: String): Boolean {
        try {
            val groupId = currentGroup.value?.id ?: return false

            // Call the repository method to remove the member and kick them
            return groupRepository.removeMemberAndKick(groupId, memberName)
        } catch (e: Exception) {
            println("ExpenseListViewModel: Error removing member and kicking: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * Check if the current user is the creator of the current group
     * @return True if the current user is the creator, false otherwise
     */
    fun isCurrentUserGroupCreator(): Boolean {
        val group = currentGroup.value
        if (group == null) {
            println("ExpenseListViewModel: isCurrentUserGroupCreator - No current group")
            return false
        }

        val isCreator = groupRepository.isCurrentUserGroupCreator(group.id)
        val currentUser = groupRepository.getSavedUserForGroup(group.id) ?: ""
        println("ExpenseListViewModel: isCurrentUserGroupCreator - Group ID: ${group.id}, Is Creator: $isCreator")
        println("ExpenseListViewModel: Group details - Name: ${group.name}, Current User: $currentUser, Members: ${group.members}, AllowedUsers: ${group.allowedUsers}")

        return isCreator
    }

    /**
     * Share an invitation link for the current group
     * @param groupId The ID of the group to share
     * @param groupName The name of the group
     */
    fun shareInvitationLink(groupId: String, groupName: String) {
        println("ExpenseListViewModel: shareInvitationLink called with groupId=$groupId, groupName=$groupName")
        try {
            // Use the InvitationLinkUtil to share the link
            val context = groupRepository.getContext()
            println("ExpenseListViewModel: Got context: $context")

            println("ExpenseListViewModel: Calling InvitationLinkUtil.shareInvitationLink")
            com.example.splitexpenses.util.InvitationLinkUtil.shareInvitationLink(context, groupId, groupName)
            println("ExpenseListViewModel: InvitationLinkUtil.shareInvitationLink called successfully")
        } catch (e: Exception) {
            println("ExpenseListViewModel: Error in shareInvitationLink: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * Update the group name
     * @param newName The new name for the group
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateGroupName(newName: String): Boolean {
        try {
            val groupId = currentGroup.value?.id ?: return false

            // Check if the current user is the creator
            if (!isCurrentUserGroupCreator()) {
                println("ExpenseListViewModel: Only the creator can update the group name")
                return false
            }

            return groupRepository.updateGroupName(groupId, newName)
        } catch (e: Exception) {
            println("ExpenseListViewModel: Error updating group name: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * Update the current user's avatar
     * @param avatarEmoji The emoji to use as the avatar, or null to remove the avatar
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateCurrentUserAvatar(avatarEmoji: String?): Boolean {
        return if (isConnected()) {
            try {
                val groupId = currentGroup.value?.id ?: return false
                val currentUserName = groupRepository.getSavedUserForGroup(groupId) ?: return false

                groupRepository.updateMemberAvatar(groupId, currentUserName, avatarEmoji)
            } catch (e: Exception) {
                println("ExpenseListViewModel: Error updating user avatar: ${e.message}")
                e.printStackTrace()
                false
            }
        } else {
            println("ExpenseListViewModel: Cannot update avatar while offline")
            false
        }
    }

    /**
     * Update the current user's name
     * @param newName The new name for the current user
     * @return True if the update was successful, false otherwise
     */
    suspend fun updateCurrentUserName(newName: String): Boolean {
        return if (isConnected()) {
            try {
                val groupId = currentGroup.value?.id ?: return false
                val currentUserName = groupRepository.getSavedUserForGroup(groupId) ?: return false

                groupRepository.updateMemberName(groupId, currentUserName, newName)
            } catch (e: Exception) {
                println("ExpenseListViewModel: Error updating user name: ${e.message}")
                e.printStackTrace()
                false
            }
        } else {
            println("ExpenseListViewModel: Cannot update name while offline")
            false
        }
    }

    /**
     * Get the avatar for a member
     * @param memberName The name of the member
     * @return The avatar emoji or null if not found
     */
    fun getMemberAvatar(memberName: String): String? {
        val groupId = currentGroup.value?.id ?: return null
        return groupRepository.getMemberAvatar(groupId, memberName)
    }

    // Filter-related methods

    /**
     * Update the filter state
     */
    fun updateFilterState(newFilterState: ExpenseFilterState) {
        updateState { state ->
            state.copy(filterState = newFilterState)
        }
    }

    /**
     * Clear all filters
     */
    fun clearAllFilters() {
        updateState { state ->
            state.copy(filterState = ExpenseFilterState())
        }
    }

    /**
     * Update search text
     */
    fun updateSearchText(searchText: String) {
        updateState { state ->
            state.copy(filterState = state.filterState.copy(searchText = searchText))
        }
    }

    /**
     * Update date range
     */
    fun updateDateRange(startDate: Long?, endDate: Long?) {
        updateState { state ->
            state.copy(
                filterState = state.filterState.copy(
                    startDate = startDate,
                    endDate = endDate,
                    quickDateFilter = QuickDateFilter.CUSTOM
                )
            )
        }
    }

    /**
     * Update period type
     */
    fun updatePeriodType(periodType: PeriodType) {
        updateState { state ->
            val currentCalendar = Calendar.getInstance()
            state.copy(
                filterState = state.filterState.copy(
                    periodType = periodType,
                    selectedMonth = if (periodType == PeriodType.THIS_MONTH) currentCalendar.get(Calendar.MONTH) else state.filterState.selectedMonth,
                    selectedYear = when (periodType) {
                        PeriodType.THIS_MONTH, PeriodType.THIS_YEAR -> currentCalendar.get(Calendar.YEAR)
                        else -> state.filterState.selectedYear
                    },
                    startDate = if (periodType == PeriodType.CUSTOM) state.filterState.startDate else null,
                    endDate = if (periodType == PeriodType.CUSTOM) state.filterState.endDate else null
                )
            )
        }
    }

    /**
     * Update selected month and year for month/year filtering
     */
    fun updateSelectedMonthYear(month: Int, year: Int) {
        updateState { state ->
            state.copy(
                filterState = state.filterState.copy(
                    selectedMonth = month,
                    selectedYear = year
                )
            )
        }
    }

    /**
     * Toggle category filter
     */
    fun toggleCategoryFilter(category: String) {
        updateState { state ->
            val currentCategories = state.filterState.selectedCategories
            val newCategories = if (category in currentCategories) {
                currentCategories - category
            } else {
                currentCategories + category
            }
            state.copy(filterState = state.filterState.copy(selectedCategories = newCategories))
        }
    }

    /**
     * Toggle member filter
     */
    fun toggleMemberFilter(member: String) {
        updateState { state ->
            val currentMembers = state.filterState.selectedMembers
            val newMembers = if (member in currentMembers) {
                currentMembers - member
            } else {
                currentMembers + member
            }
            state.copy(filterState = state.filterState.copy(selectedMembers = newMembers))
        }
    }

    /**
     * Check if any filters are currently active
     */
    fun hasActiveFilters(): Boolean {
        return uiState.value.filterState.hasActiveFilters()
    }

    /**
     * Get the number of active filters
     */
    fun getActiveFilterCount(): Int {
        return uiState.value.filterState.getActiveFilterCount()
    }
}
