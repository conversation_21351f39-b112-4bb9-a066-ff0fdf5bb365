package com.example.splitexpenses.ui.viewmodels;

/**
 * UI state for the categories management screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b(\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u009d\u0001\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0006\u0012\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\u0006\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0004\u0012\b\b\u0002\u0010\r\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0006\u0012\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u000b\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0002\u0010\u0013J\u000f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\'\u001a\u00020\u000bH\u00c6\u0003J\u000b\u0010(\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010)\u001a\u00020\u0006H\u00c6\u0003J\t\u0010*\u001a\u00020\u0006H\u00c6\u0003J\u000f\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\t\u0010,\u001a\u00020\u0006H\u00c6\u0003J\t\u0010-\u001a\u00020\u000bH\u00c6\u0003J\u000b\u0010.\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\t\u0010/\u001a\u00020\u0006H\u00c6\u0003J\t\u00100\u001a\u00020\u0006H\u00c6\u0003J\u00a1\u0001\u00101\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\b\b\u0002\u0010\t\u001a\u00020\u00062\b\b\u0002\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00042\b\b\u0002\u0010\r\u001a\u00020\u00062\b\b\u0002\u0010\u000e\u001a\u00020\u00062\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\b\b\u0002\u0010\u0010\u001a\u00020\u00062\b\b\u0002\u0010\u0011\u001a\u00020\u000b2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u00102\u001a\u00020\u000b2\b\u00103\u001a\u0004\u0018\u000104H\u00d6\u0003J\t\u00105\u001a\u000206H\u00d6\u0001J\t\u00107\u001a\u00020\u0006H\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u000e\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0010\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0017R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0015R\u0011\u0010\r\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0017R\u0013\u0010\f\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0016\u0010\u0012\u001a\u0004\u0018\u00010\u0006X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0017R\u0014\u0010\u0011\u001a\u00020\u000bX\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u001eR\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0017R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0015R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0017R\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0017R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001e\u00a8\u00068"}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/CategoriesUiState;", "Lcom/example/splitexpenses/ui/viewmodels/UiState;", "categories", "", "Lcom/example/splitexpenses/data/Category;", "newCategoryName", "", "newCategoryEmoji", "newCategoryKeywords", "newKeywordInput", "showError", "", "editingCategory", "editName", "editEmoji", "editKeywords", "editKeywordInput", "isLoading", "error", "(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;ZLcom/example/splitexpenses/data/Category;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;ZLjava/lang/String;)V", "getCategories", "()Ljava/util/List;", "getEditEmoji", "()Ljava/lang/String;", "getEditKeywordInput", "getEditKeywords", "getEditName", "getEditingCategory", "()Lcom/example/splitexpenses/data/Category;", "getError", "()Z", "getNewCategoryEmoji", "getNewCategoryKeywords", "getNewCategoryName", "getNewKeywordInput", "getShowError", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "", "hashCode", "", "toString", "app_debug"})
public final class CategoriesUiState implements com.example.splitexpenses.ui.viewmodels.UiState {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.splitexpenses.data.Category> categories = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String newCategoryName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String newCategoryEmoji = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> newCategoryKeywords = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String newKeywordInput = null;
    private final boolean showError = false;
    @org.jetbrains.annotations.Nullable()
    private final com.example.splitexpenses.data.Category editingCategory = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String editName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String editEmoji = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> editKeywords = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String editKeywordInput = null;
    private final boolean isLoading = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String error = null;
    
    public CategoriesUiState(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Category> categories, @org.jetbrains.annotations.NotNull()
    java.lang.String newCategoryName, @org.jetbrains.annotations.NotNull()
    java.lang.String newCategoryEmoji, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> newCategoryKeywords, @org.jetbrains.annotations.NotNull()
    java.lang.String newKeywordInput, boolean showError, @org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.Category editingCategory, @org.jetbrains.annotations.NotNull()
    java.lang.String editName, @org.jetbrains.annotations.NotNull()
    java.lang.String editEmoji, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> editKeywords, @org.jetbrains.annotations.NotNull()
    java.lang.String editKeywordInput, boolean isLoading, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.data.Category> getCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNewCategoryName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNewCategoryEmoji() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getNewCategoryKeywords() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getNewKeywordInput() {
        return null;
    }
    
    public final boolean getShowError() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.splitexpenses.data.Category getEditingCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getEditName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getEditEmoji() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getEditKeywords() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getEditKeywordInput() {
        return null;
    }
    
    @java.lang.Override()
    public boolean isLoading() {
        return false;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.String getError() {
        return null;
    }
    
    public CategoriesUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.splitexpenses.data.Category> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    public final boolean component12() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.splitexpenses.data.Category component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.splitexpenses.ui.viewmodels.CategoriesUiState copy(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.splitexpenses.data.Category> categories, @org.jetbrains.annotations.NotNull()
    java.lang.String newCategoryName, @org.jetbrains.annotations.NotNull()
    java.lang.String newCategoryEmoji, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> newCategoryKeywords, @org.jetbrains.annotations.NotNull()
    java.lang.String newKeywordInput, boolean showError, @org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.Category editingCategory, @org.jetbrains.annotations.NotNull()
    java.lang.String editName, @org.jetbrains.annotations.NotNull()
    java.lang.String editEmoji, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> editKeywords, @org.jetbrains.annotations.NotNull()
    java.lang.String editKeywordInput, boolean isLoading, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}