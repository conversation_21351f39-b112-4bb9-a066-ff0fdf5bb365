package com.example.splitexpenses.util

import com.example.splitexpenses.data.Category
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.util.Date

class CsvUtilTest {

    private lateinit var testGroup: GroupData
    private lateinit var testExpense: Expense

    @Before
    fun setup() {
        // Create test data
        testExpense = Expense(
            id = "test-expense-id",
            amount = 50.0,
            description = "Test Expense",
            paidBy = "Alice",
            splitBetween = listOf("Alice", "Bob"),
            category = "Food",
            date = Date().time,
            timestamp = Date().time,
            isCategoryLocked = true
        )

        val testCategories = listOf(
            Category("Food", "🍔", listOf("food", "restaurant")),
            Category("Transport", "🚗", listOf("car", "bus"))
        )

        val testMemberAvatars = mapOf(
            "Alice" to "😊",
            "<PERSON>" to "😎"
        )

        testGroup = GroupData(
            id = "test-group-id",
            name = "Test Group",
            members = listOf("Alice", "Bob"),
            expenses = listOf(testExpense),
            memberUidMap = mapOf("Alice" to "device1"),
            allowedUsers = listOf("device1"),
            creatorUid = "device1",
            categories = testCategories,
            memberAvatars = testMemberAvatars
        )
    }

    @Test
    fun `test export and import round trip`() {
        // Export the group to CSV
        val outputStream = ByteArrayOutputStream()
        val exportResult = CsvUtil.exportGroupToCsv(testGroup, outputStream)
        assertTrue("Export should succeed", exportResult)

        // Get the CSV content
        val csvContent = outputStream.toString()
        println("Exported CSV content:\n$csvContent")

        // Import the CSV content back
        val inputStream = ByteArrayInputStream(csvContent.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Verify the import was successful
        assertTrue("Import should succeed", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        // Verify the imported group data
        val importedGroup = importResult.group!!
        assertEquals("Group name should match", testGroup.name, importedGroup.name)
        assertEquals("Members should match", testGroup.members, importedGroup.members)

        // Verify categories were imported correctly
        assertEquals("Number of categories should match", testGroup.categories.size, importedGroup.categories.size)
        assertEquals("Category names should match",
            testGroup.categories.map { it.name },
            importedGroup.categories.map { it.name }
        )
        assertEquals("Category emojis should match",
            testGroup.categories.map { it.emoji },
            importedGroup.categories.map { it.emoji }
        )

        // Verify member avatars were imported correctly
        assertEquals("Member avatars should match", testGroup.memberAvatars, importedGroup.memberAvatars)

        // Note: User management fields (memberUidMap, creatorUid, allowedUsers) are not exported/imported
        // as they are internal Firebase fields managed by the repository

        // Verify expenses were imported correctly
        assertEquals("Number of expenses should match", testGroup.expenses.size, importedGroup.expenses.size)
        val importedExpense = importedGroup.expenses.first()
        assertEquals("Expense amount should match", testExpense.amount, importedExpense.amount, 0.001)
        assertEquals("Expense description should match", testExpense.description, importedExpense.description)
        assertEquals("Expense paidBy should match", testExpense.paidBy, importedExpense.paidBy)
        assertEquals("Expense splitBetween should match", testExpense.splitBetween, importedExpense.splitBetween)
        assertEquals("Expense category should match", testExpense.category, importedExpense.category)
        assertEquals("Expense isCategoryLocked should match", testExpense.isCategoryLocked, importedExpense.isCategoryLocked)
    }

    @Test
    fun `test import with trailing empty fields`() {
        // Create a CSV with trailing semicolons (empty fields) that should be automatically trimmed
        val csvWithTrailingFields = """
            name;members;memberAvatars;categories;;;
            Test Group;Alice|Bob;Alice=😊;Bob=😎;Food~🍔~food^restaurant;Transport~🚗~car^bus;;
            amount;description;paidBy;splitBetween;category;date;isCategoryLocked;;
            50.00;Test Expense;Alice;Alice|Bob;Food;2024-01-01;true;;
        """.trimIndent()

        println("CSV with trailing fields:\n$csvWithTrailingFields")

        // Import the CSV content
        val inputStream = ByteArrayInputStream(csvWithTrailingFields.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Print any errors for debugging
        if (importResult.errors.isNotEmpty()) {
            println("Import errors:")
            importResult.errors.forEach { error ->
                println("  Line ${error.lineNumber}: ${error.message}")
            }
        }

        // Print any warnings for debugging
        if (importResult.warnings.isNotEmpty()) {
            println("Import warnings:")
            importResult.warnings.forEach { warning ->
                println("  Line ${warning.lineNumber}: ${warning.message}")
            }
        }

        // Verify the import was successful despite trailing empty fields
        assertTrue("Import should succeed even with trailing empty fields", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        // Verify the imported group data
        val importedGroup = importResult.group!!
        assertEquals("Group name should match", "Test Group", importedGroup.name)
        assertEquals("Members should match", listOf("Alice", "Bob"), importedGroup.members)

        // Verify expenses were imported correctly
        assertEquals("Number of expenses should match", 1, importedGroup.expenses.size)
        val importedExpense = importedGroup.expenses.first()
        assertEquals("Expense amount should match", 50.0, importedExpense.amount, 0.001)
        assertEquals("Expense description should match", "Test Expense", importedExpense.description)
        assertEquals("Expense paidBy should match", "Alice", importedExpense.paidBy)
        assertEquals("Expense splitBetween should match", listOf("Alice", "Bob"), importedExpense.splitBetween)
        assertEquals("Expense category should match", "Food", importedExpense.category)
        assertEquals("Expense isCategoryLocked should match", true, importedExpense.isCategoryLocked)
    }

    @Test
    fun `test import with various trailing delimiter patterns`() {
        // Test different patterns of trailing delimiters
        val testCases = listOf(
            "name;members;memberAvatars;categories;",      // Single trailing delimiter
            "name;members;memberAvatars;categories;;",     // Double trailing delimiter
            "name;members;memberAvatars;categories;;;",    // Triple trailing delimiter
            "name;members;memberAvatars;categories;;;;;",  // Many trailing delimiters
            "name;members;memberAvatars;categories;;;;;;;;;;;;;;;" // Extreme trailing delimiters
        )

        testCases.forEach { headerLine ->
            println("Testing header: '$headerLine'")

            val csvContent = """
                $headerLine
                Test Group;Alice|Bob;;
                amount;description;paidBy;splitBetween;category;date;isCategoryLocked
                50.00;Test Expense;Alice;Alice|Bob;Food;2024-01-01;true
            """.trimIndent()

            val inputStream = ByteArrayInputStream(csvContent.toByteArray())
            val importResult = CsvUtil.importGroupFromCsv(inputStream)

            // Print any errors for debugging
            if (importResult.errors.isNotEmpty()) {
                println("  Import errors for '$headerLine':")
                importResult.errors.forEach { error ->
                    println("    Line ${error.lineNumber}: ${error.message}")
                }
            }

            // Print any warnings for debugging
            if (importResult.warnings.isNotEmpty()) {
                println("  Import warnings for '$headerLine':")
                importResult.warnings.forEach { warning ->
                    println("    Line ${warning.lineNumber}: ${warning.message}")
                }
            }

            // Each test case should succeed
            assertTrue("Import should succeed for header: '$headerLine'", importResult.success)
            assertTrue("Import should have no errors for header: '$headerLine'", importResult.errors.isEmpty())

            val importedGroup = importResult.group!!
            assertEquals("Group name should match for header: '$headerLine'", "Test Group", importedGroup.name)
        }
    }

    @Test
    fun `test delimiter detection with semicolon CSV`() {
        // Test that semicolon delimiter is correctly detected
        val csvContent = """
            name;members;memberAvatars;categories
            Test Group;Alice|Bob;;
            amount;description;paidBy;splitBetween;category;date;isCategoryLocked
            50.00;Test Expense;Alice;Alice|Bob;Food;2024-01-01;true
        """.trimIndent()

        val inputStream = ByteArrayInputStream(csvContent.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Print any errors for debugging
        if (importResult.errors.isNotEmpty()) {
            println("Import errors:")
            importResult.errors.forEach { error ->
                println("  Line ${error.lineNumber}: ${error.message}")
            }
        }

        assertTrue("Import should succeed with semicolon delimiter", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        val importedGroup = importResult.group!!
        assertEquals("Group name should match", "Test Group", importedGroup.name)
        assertEquals("Members should match", listOf("Alice", "Bob"), importedGroup.members)
    }



    @Test
    fun `test extreme trailing fields case`() {
        // Test the exact case from the error message
        val csvContent = """
            name;members;memberAvatars;categories;;;;;;;;;;;;;;
            Test Group;Alice|Bob;;
            amount;description;paidBy;splitBetween;category;date;isCategoryLocked
            50.00;Test Expense;Alice;Alice|Bob;Food;2024-01-01;true
        """.trimIndent()

        println("Testing extreme trailing fields CSV:")
        println(csvContent)

        val inputStream = ByteArrayInputStream(csvContent.toByteArray())
        val importResult = CsvUtil.importGroupFromCsv(inputStream)

        // Print any errors for debugging
        if (importResult.errors.isNotEmpty()) {
            println("Import errors:")
            importResult.errors.forEach { error ->
                println("  Line ${error.lineNumber}: ${error.message}")
            }
        }

        // Print any warnings for debugging
        if (importResult.warnings.isNotEmpty()) {
            println("Import warnings:")
            importResult.warnings.forEach { warning ->
                println("  Line ${warning.lineNumber}: ${warning.message}")
            }
        }

        assertTrue("Import should succeed with extreme trailing fields", importResult.success)
        assertTrue("Import should have no errors", importResult.errors.isEmpty())

        val importedGroup = importResult.group!!
        assertEquals("Group name should match", "Test Group", importedGroup.name)
        assertEquals("Members should match", listOf("Alice", "Bob"), importedGroup.members)
    }
}
