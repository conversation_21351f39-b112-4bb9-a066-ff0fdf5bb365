package com.example.splitexpenses.ui.viewmodels

import com.example.splitexpenses.data.Category

/**
 * UI state for the categories management screen
 */
data class CategoriesUiState(
    val categories: List<Category> = emptyList(),
    val newCategoryName: String = "",
    val newCategoryEmoji: String = "",
    val newCategoryKeywords: List<String> = emptyList(),
    val newKeywordInput: String = "",
    val showError: Boolean = false,
    val editingCategory: Category? = null,
    val editName: String = "",
    val editEmoji: String = "",
    val editKeywords: List<String> = emptyList(),
    val editKeywordInput: String = "",
    override val isLoading: Boolean = false,
    override val error: String? = null
) : UiState
