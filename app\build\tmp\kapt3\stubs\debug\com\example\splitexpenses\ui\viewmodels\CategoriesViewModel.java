package com.example.splitexpenses.ui.viewmodels;

/**
 * ViewModel for the categories management screen
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u000e\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\f\u001a\u00020\rJ\u0006\u0010\u000e\u001a\u00020\rJ\u0006\u0010\u000f\u001a\u00020\rJ\u0006\u0010\u0010\u001a\u00020\rJ\u0006\u0010\u0011\u001a\u00020\rJ\u000e\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0013\u001a\u00020\u0014J\u000e\u0010\u0015\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u0017J\u000e\u0010\u0018\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u0017J\u0006\u0010\u0019\u001a\u00020\rJ\u0006\u0010\u001a\u001a\u00020\rJ\u000e\u0010\u001b\u001a\u00020\r2\u0006\u0010\u0013\u001a\u00020\u0014J\u000e\u0010\u001c\u001a\u00020\r2\u0006\u0010\u001d\u001a\u00020\u0017J\u000e\u0010\u001e\u001a\u00020\r2\u0006\u0010\u001f\u001a\u00020\u0017J\u000e\u0010 \u001a\u00020\r2\u0006\u0010!\u001a\u00020\u0017J\u000e\u0010\"\u001a\u00020\r2\u0006\u0010\u001d\u001a\u00020\u0017J\u000e\u0010#\u001a\u00020\r2\u0006\u0010!\u001a\u00020\u0017J\u000e\u0010$\u001a\u00020\r2\u0006\u0010\u001f\u001a\u00020\u0017R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006%"}, d2 = {"Lcom/example/splitexpenses/ui/viewmodels/CategoriesViewModel;", "Landroidx/lifecycle/ViewModel;", "groupRepository", "Lcom/example/splitexpenses/data/repositories/GroupRepository;", "(Lcom/example/splitexpenses/data/repositories/GroupRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/splitexpenses/ui/viewmodels/CategoriesUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "addCategory", "", "addEditKeyword", "addNewKeyword", "cancelEditingCategory", "clearError", "deleteCategory", "category", "Lcom/example/splitexpenses/data/Category;", "removeEditKeyword", "keyword", "", "removeNewKeyword", "saveCategories", "saveEditedCategory", "startEditingCategory", "updateEditEmoji", "emoji", "updateEditKeywordInput", "input", "updateEditName", "name", "updateNewCategoryEmoji", "updateNewCategoryName", "updateNewKeywordInput", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class CategoriesViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.splitexpenses.data.repositories.GroupRepository groupRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.splitexpenses.ui.viewmodels.CategoriesUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.ui.viewmodels.CategoriesUiState> uiState = null;
    
    @javax.inject.Inject()
    public CategoriesViewModel(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.repositories.GroupRepository groupRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.splitexpenses.ui.viewmodels.CategoriesUiState> getUiState() {
        return null;
    }
    
    /**
     * Update the new category name
     */
    public final void updateNewCategoryName(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
    }
    
    /**
     * Update the new category emoji
     */
    public final void updateNewCategoryEmoji(@org.jetbrains.annotations.NotNull()
    java.lang.String emoji) {
    }
    
    /**
     * Update the new keyword input field
     */
    public final void updateNewKeywordInput(@org.jetbrains.annotations.NotNull()
    java.lang.String input) {
    }
    
    /**
     * Add a keyword to the new category
     */
    public final void addNewKeyword() {
    }
    
    /**
     * Remove a keyword from the new category
     */
    public final void removeNewKeyword(@org.jetbrains.annotations.NotNull()
    java.lang.String keyword) {
    }
    
    /**
     * Add a new category
     */
    public final void addCategory() {
    }
    
    /**
     * Delete a category (prevents deletion of the default "None" category)
     */
    public final void deleteCategory(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.Category category) {
    }
    
    /**
     * Start editing a category
     */
    public final void startEditingCategory(@org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.Category category) {
    }
    
    /**
     * Update the edit name
     */
    public final void updateEditName(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
    }
    
    /**
     * Update the edit emoji
     */
    public final void updateEditEmoji(@org.jetbrains.annotations.NotNull()
    java.lang.String emoji) {
    }
    
    /**
     * Update the edit keyword input field
     */
    public final void updateEditKeywordInput(@org.jetbrains.annotations.NotNull()
    java.lang.String input) {
    }
    
    /**
     * Add a keyword to the editing category
     */
    public final void addEditKeyword() {
    }
    
    /**
     * Remove a keyword from the editing category
     */
    public final void removeEditKeyword(@org.jetbrains.annotations.NotNull()
    java.lang.String keyword) {
    }
    
    /**
     * Save the edited category
     */
    public final void saveEditedCategory() {
    }
    
    /**
     * Cancel editing a category
     */
    public final void cancelEditingCategory() {
    }
    
    /**
     * Save all categories to the current group
     */
    public final void saveCategories() {
    }
    
    /**
     * Clear the error state
     */
    public final void clearError() {
    }
}