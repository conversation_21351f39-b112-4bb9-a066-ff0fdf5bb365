package com.example.splitexpenses.ui.navigation
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.example.splitexpenses.ui.navigation.navigateWithoutAnimation
import com.example.splitexpenses.ui.navigation.popBackStackWithoutAnimation
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.ui.components.BalanceDetailsScreen
import com.example.splitexpenses.ui.components.ExpenseDetailsScreen
import com.example.splitexpenses.ui.components.ExpenseEditScreen
import com.example.splitexpenses.ui.components.ExpenseListScreen
import com.example.splitexpenses.ui.components.GroupListScreen
import com.example.splitexpenses.ui.components.ManageCategoriesScreen
import com.example.splitexpenses.ui.components.StatisticsScreen
import com.example.splitexpenses.ui.navigation.NavDestinations.BALANCE_DETAILS_ROUTE
import com.example.splitexpenses.ui.navigation.NavDestinations.EXPENSE_DETAILS_ROUTE
import com.example.splitexpenses.ui.navigation.NavDestinations.MANAGE_CATEGORIES_ROUTE
import com.example.splitexpenses.ui.navigation.NavDestinations.EXPENSE_DETAILS_ROUTE_WITH_PARAMS
import com.example.splitexpenses.ui.navigation.NavDestinations.EXPENSE_EDIT_ROUTE
import com.example.splitexpenses.ui.navigation.NavDestinations.EXPENSE_EDIT_ROUTE_WITH_PARAMS
import com.example.splitexpenses.ui.navigation.NavDestinations.EXPENSE_ID_ARG
import com.example.splitexpenses.ui.navigation.NavDestinations.EXPENSE_LIST_ROUTE
import com.example.splitexpenses.ui.navigation.NavDestinations.EXPENSE_LIST_ROUTE_WITH_PARAMS
import com.example.splitexpenses.ui.navigation.NavDestinations.GROUP_ID_ARG
import com.example.splitexpenses.ui.navigation.NavDestinations.GROUP_LIST_ROUTE
import com.example.splitexpenses.ui.navigation.NavDestinations.STATISTICS_ROUTE
import com.example.splitexpenses.ui.viewmodels.BalanceDetailsViewModel
import com.example.splitexpenses.ui.viewmodels.CategoriesViewModel
import com.example.splitexpenses.ui.viewmodels.ExpenseListViewModel
import com.example.splitexpenses.ui.viewmodels.GroupListViewModel
import java.io.OutputStream

// No custom animation functions needed

/**
 * Main navigation host for the SplitExpenses app
 */
@Composable
fun SplitExpensesNavHost(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    startDestination: String = GROUP_LIST_ROUTE,
    onShowCreateGroupDialog: () -> Unit = {},
    onShowJoinGroupDialog: () -> Unit = {},
    onShowManageMembersDialog: () -> Unit = {},
    onShowDeleteGroupDialog: () -> Unit = {},
    onShowDeleteExpenseDialog: (String) -> Unit = {},
    onShowEditGroupNameDialog: () -> Unit = {},
    onShowEditMemberInfoDialog: () -> Unit = {}
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier,
        // Use slide animations for transitions between destinations
        enterTransition = {
            androidx.compose.animation.slideInHorizontally(initialOffsetX = { it }) // Slide in from right
        },
        exitTransition = {
            androidx.compose.animation.slideOutHorizontally(targetOffsetX = { -it }) // Slide out to left
        },
        // Use slide animations when returning to a previous destination
        popEnterTransition = {
            androidx.compose.animation.slideInHorizontally(initialOffsetX = { -it }) // Slide in from left
        },
        popExitTransition = {
            androidx.compose.animation.slideOutHorizontally(targetOffsetX = { it }) // Slide out to right
        }
    ) {
        // Group List Screen
        composable(
            route = GROUP_LIST_ROUTE
        ) {
            val viewModel: GroupListViewModel = hiltViewModel()
            val uiState by viewModel.uiState.collectAsState()
            val groups by viewModel.availableGroups.collectAsState()
            val isLoading by viewModel.isLoadingGroups.collectAsState()
            val pendingSyncCount by viewModel.pendingSyncCount.collectAsState()

            GroupListScreen(
                groups = groups,
                onGroupClick = { groupId, member ->
                    viewModel.joinGroup(groupId, member)
                    navController.navigateWithoutAnimation("$EXPENSE_LIST_ROUTE/$groupId")
                },
                onCreateGroupClick = onShowCreateGroupDialog,
                onJoinGroupClick = onShowJoinGroupDialog,
                isMultiSelectMode = uiState.isMultiSelectMode,
                onMultiSelectModeChange = viewModel::setMultiSelectMode,
                selectedGroups = uiState.selectedGroups,
                onSelectedGroupsChange = { groupId, selected ->
                    viewModel.toggleGroupSelection(groupId, selected)
                },
                onDeleteSelectedGroups = {
                    viewModel.deleteGroups(uiState.selectedGroups)
                },
                getSavedUserForGroup = viewModel::getSavedUserForGroup,
                isCurrentUserGroupCreator = viewModel::isCurrentUserGroupCreator,
                isLoading = isLoading,
                isOffline = !viewModel.isConnected.collectAsState().value,
                pendingSyncCount = pendingSyncCount
            )
        }

        // Expense List Screen
        composable(
            route = EXPENSE_LIST_ROUTE_WITH_PARAMS,
            arguments = listOf(
                navArgument(GROUP_ID_ARG) { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val groupId = backStackEntry.arguments?.getString(GROUP_ID_ARG) ?: ""
            val viewModel: ExpenseListViewModel = hiltViewModel()
            val uiState by viewModel.uiState.collectAsState()
            val currentGroup by viewModel.currentGroup.collectAsState()

            // If the group is not loaded yet, we need to join it
            if (currentGroup == null) {
                // This will be handled by the MainActivity for now
                // We'll need to show a loading indicator or handle this case better
            }

            currentGroup?.let { group ->
                val uiState by viewModel.uiState.collectAsState()

                ExpenseListScreen(
                    group = group,
                    onExpenseClick = { expense ->
                        navController.navigateWithSlideAnimation("$EXPENSE_DETAILS_ROUTE/${expense.id}")
                    },
                    onShowBalanceDetailsClick = {
                        navController.navigateWithSlideAnimation(BALANCE_DETAILS_ROUTE)
                    },
                    onAddExpenseClick = {
                        navController.navigateWithSlideAnimation(EXPENSE_EDIT_ROUTE)
                    },
                    onDeleteExpense = { expenseIds ->
                        viewModel.deleteExpenses(expenseIds)
                    },
                    onShowStatisticsClick = {
                        navController.navigateWithSlideAnimation(STATISTICS_ROUTE)
                    },
                    onShowManageMembersClick = onShowManageMembersDialog,
                    onShowDeleteGroupDialog = onShowDeleteGroupDialog,
                    onExportToCsv = { outputStream ->
                        viewModel.exportToCsv(outputStream)
                    },
                    isMultiSelectMode = uiState.isMultiSelectMode,
                    onMultiSelectModeChange = viewModel::setMultiSelectMode,
                    selectedExpenses = uiState.selectedExpenses,
                    onSelectedExpensesChange = { expenseId, selected ->
                        viewModel.toggleExpenseSelection(expenseId, selected)
                    },
                    onNavigateToManageCategories = {
                        navController.navigateWithSlideAnimation(MANAGE_CATEGORIES_ROUTE)
                    },
                    isCurrentUserGroupCreator = viewModel.isCurrentUserGroupCreator(),
                    onEditGroupName = onShowEditGroupNameDialog,
                    isOffline = !viewModel.isConnected.collectAsState().value,
                    filterState = uiState.filterState,
                    onFilterStateChange = viewModel::updateFilterState
                )
            }
        }

        // Expense Details Screen
        composable(
            route = EXPENSE_DETAILS_ROUTE_WITH_PARAMS,
            arguments = listOf(
                navArgument(EXPENSE_ID_ARG) { type = NavType.StringType }
            ),
            // Use slide animations when coming from expense list, but disable for swipe navigation
            enterTransition = {
                // Check if we're coming from expense list (normal navigation)
                if (initialState.destination.route == EXPENSE_LIST_ROUTE_WITH_PARAMS) {
                    androidx.compose.animation.slideInHorizontally(initialOffsetX = { it })
                } else {
                    // Disable animations for swipe navigation between expenses
                    EnterTransition.None
                }
            },
            exitTransition = {
                // Check if we're going back to expense list
                if (targetState.destination.route == EXPENSE_LIST_ROUTE_WITH_PARAMS) {
                    androidx.compose.animation.slideOutHorizontally(targetOffsetX = { -it })
                } else {
                    // Disable animations for swipe navigation between expenses
                    ExitTransition.None
                }
            },
            popEnterTransition = {
                // When returning from edit screen, use slide animation
                androidx.compose.animation.slideInHorizontally(initialOffsetX = { -it })
            },
            popExitTransition = {
                // When going back to expense list, use slide animation
                androidx.compose.animation.slideOutHorizontally(targetOffsetX = { it })
            }
        ) { backStackEntry ->
            val expenseId = backStackEntry.arguments?.getString(EXPENSE_ID_ARG) ?: ""
            val viewModel: ExpenseListViewModel = hiltViewModel()
            val currentGroup by viewModel.currentGroup.collectAsState()

            currentGroup?.let { group ->
                val expense = group.expenses.find { it.id == expenseId }
                expense?.let {
                    ExpenseDetailsScreen(
                        expense = it,
                        group = group,
                        onBackClick = {
                            navController.popBackStack()
                        },
                        onEditClick = {
                            navController.navigateWithSlideAnimation("$EXPENSE_EDIT_ROUTE?expenseId=$expenseId")
                        },
                        onPreviousClick = {
                            val currentIndex = group.expenses.indexOf(it)
                            if (currentIndex > 0) {
                                val previousExpense = group.expenses[currentIndex - 1]

                                // Create a new composable destination without animations
                                val route = "$EXPENSE_DETAILS_ROUTE/${previousExpense.id}"

                                // Navigate without animations
                                navController.navigateWithoutAnimation(route) {
                                    popUpTo(navController.currentBackStackEntry?.destination?.route ?: "") {
                                        inclusive = true
                                    }
                                }
                            }
                        },
                        onNextClick = {
                            val currentIndex = group.expenses.indexOf(it)
                            if (currentIndex < group.expenses.size - 1) {
                                val nextExpense = group.expenses[currentIndex + 1]

                                // Create a new composable destination without animations
                                val route = "$EXPENSE_DETAILS_ROUTE/${nextExpense.id}"

                                // Navigate without animations
                                navController.navigateWithoutAnimation(route) {
                                    popUpTo(navController.currentBackStackEntry?.destination?.route ?: "") {
                                        inclusive = true
                                    }
                                }
                            }
                        },
                        hasPrevious = group.expenses.indexOf(it) > 0,
                        hasNext = group.expenses.indexOf(it) < group.expenses.size - 1,
                        onNavigateToExpense = { targetExpenseId ->
                            // Direct navigation to any expense by ID
                            val route = "$EXPENSE_DETAILS_ROUTE/$targetExpenseId"
                            navController.navigateWithoutAnimation(route) {
                                popUpTo(navController.currentBackStackEntry?.destination?.route ?: "") {
                                    inclusive = true
                                }
                            }
                        },
                        isOffline = !viewModel.isConnected.collectAsState().value
                    )
                }
            }
        }

        // Expense Edit Screen
        composable(
            route = EXPENSE_EDIT_ROUTE_WITH_PARAMS,
            arguments = listOf(
                navArgument(EXPENSE_ID_ARG) {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            )
        ) { backStackEntry ->
            val expenseId = backStackEntry.arguments?.getString(EXPENSE_ID_ARG)
            val viewModel: ExpenseListViewModel = hiltViewModel()
            val currentGroup by viewModel.currentGroup.collectAsState()

            currentGroup?.let { group ->
                val expense = if (expenseId != null) {
                    val foundExpense = group.expenses.find { it.id == expenseId } ?: Expense()
                    println("SplitExpensesNavHost: Found expense with id=$expenseId, isCategoryLocked=${foundExpense.isCategoryLocked}")
                    foundExpense
                } else {
                    println("SplitExpensesNavHost: Creating new expense")
                    Expense()
                }

                // Get the current user from UserPreferences via the GroupRepository
                val currentUser = viewModel.groupRepository.getSavedUserForGroup(group.id) ?: ""

                ExpenseEditScreen(
                    expense = expense,
                    group = group,
                    currentUser = currentUser,
                    onSave = { amount, description, paidBy, splitBetween, category, date, isCategoryLocked ->
                        if (expense.id.isEmpty()) {
                            viewModel.addExpense(
                                amount = amount,
                                description = description,
                                paidBy = paidBy,
                                splitBetween = splitBetween,
                                category = category,
                                date = date,
                                isCategoryLocked = isCategoryLocked
                            )
                        } else {
                            viewModel.updateExpense(
                                expenseId = expense.id,
                                amount = amount,
                                description = description,
                                paidBy = paidBy,
                                splitBetween = splitBetween,
                                category = category,
                                date = date,
                                isCategoryLocked = isCategoryLocked
                            )
                        }
                        navController.popBackStack()
                    },
                    onCancel = {
                        navController.popBackStack()
                    },
                    isOffline = !viewModel.isConnected.collectAsState().value,
                    onDelete = if (expense.id.isNotEmpty()) {
                        {
                            onShowDeleteExpenseDialog(expense.id)
                        }
                    } else null
                )
            }
        }

        // Balance Details Screen
        composable(
            route = BALANCE_DETAILS_ROUTE
        ) {
            // The BalanceDetailsScreen now uses hiltViewModel() internally
            // and handles its own state management
            BalanceDetailsScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }

        // Statistics Screen
        composable(
            route = STATISTICS_ROUTE
        ) {
            val viewModel: ExpenseListViewModel = hiltViewModel()
            val currentGroup by viewModel.currentGroup.collectAsState()

            currentGroup?.let { group ->
                StatisticsScreen(
                    group = group,
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
        }

        // Manage Categories Screen
        composable(
            route = MANAGE_CATEGORIES_ROUTE
        ) {
            val viewModel: CategoriesViewModel = hiltViewModel()

            ManageCategoriesScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
    }
}
